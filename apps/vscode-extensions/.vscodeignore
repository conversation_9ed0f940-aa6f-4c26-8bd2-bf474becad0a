# Source files
src/**
webview/src/**
webview/node_modules/**
webview/package.json
webview/tsconfig.json
webview/vite.config.ts

# Build configuration
esbuild.config.js
tsconfig.json
.eslintrc.json

# Development files
.vscode/**
.vscode-test/**
node_modules/**
!dist/node_modules/@xenova/**
!dist/node_modules/onnxruntime-*/**

# Documentation and misc
README.md
HYBRID_ESM_SETUP.md
.gitignore
.yarnrc
vsc-extension-quickstart.md

# Source maps (optional - remove if you want to include them)
**/*.map

# TypeScript files
**/*.ts
!**/*.d.ts

# Test files
**/test/**
**/tests/**

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Scripts
scripts/**
