const esbuild = require('esbuild');
const path = require('path');
const fs = require('fs');

const isWatch = process.argv.includes('--watch');

// Recursively get all .ts files from a directory
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    if (fs.statSync(filePath).isDirectory()) {
      fileList = getAllFiles(filePath, fileList);
    } else if (file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });
  return fileList;
}

const workerFiles = getAllFiles(path.join(__dirname, 'src/workers'));

/** Main extension: CommonJS for VSCode compatibility */
const extensionConfig = {
  entryPoints: ['src/extension.ts'],
  bundle: true,
  platform: 'node',
  target: 'node18',
  outdir: 'dist',
  format: 'cjs',
  sourcemap: true,
  minify: process.env.NODE_ENV === 'production',
  external: ['vscode'],
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },
};

/** Worker processes: ESM for @xenova/transformers support */
const workersConfig = {
  entryPoints: workerFiles,
  bundle: true,
  platform: 'node',
  target: 'node18',
  outdir: 'dist/workers',
  outbase: 'src/workers',
  format: 'esm',
  sourcemap: true,
  minify: process.env.NODE_ENV === 'production',
  external: [
    'vscode',
    'onnxruntime-node',
    'sharp',
    '@xenova/transformers'
  ],
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },
  banner: {
    js: `
import { createRequire } from 'module';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
const require = createRequire(import.meta.url);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
`
  }
};

// Ensure dist/workers/package.json exists for ESM support
async function ensureWorkersPackageJson() {
  const workersDistDir = path.join(__dirname, 'dist', 'workers');
  const packageJsonPath = path.join(workersDistDir, 'package.json');
  if (!fs.existsSync(workersDistDir)) {
    fs.mkdirSync(workersDistDir, { recursive: true });
  }
  const workersPackageJson = { type: 'module' };
  fs.writeFileSync(packageJsonPath, JSON.stringify(workersPackageJson, null, 2));
}

async function build() {
  try {
    await ensureWorkersPackageJson();
    await Promise.all([
      esbuild.build(extensionConfig),
      esbuild.build(workersConfig)
    ]);
    console.log('Build completed successfully');
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

async function watch() {
  try {
    await ensureWorkersPackageJson();
    const [extensionContext, workersContext] = await Promise.all([
      esbuild.context(extensionConfig),
      esbuild.context(workersConfig)
    ]);
    await Promise.all([
      extensionContext.watch(),
      workersContext.watch()
    ]);
    console.log('Watching for changes...');
  } catch (error) {
    console.error('Watch failed:', error);
    process.exit(1);
  }
}

if (isWatch) {
  watch();
} else {
  build();
}